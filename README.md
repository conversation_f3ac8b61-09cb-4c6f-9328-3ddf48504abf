# 📅 Scadenziario - App per la Gestione delle Scadenze

Un'applicazione Flutter completa per gestire note con scadenze e ricevere notifiche email automatiche.

## ✨ Funzionalità

- **Gestione Note**: <PERSON><PERSON>, modifica ed elimina note con date di scadenza
- **Notifiche Email**: Ricevi email automatiche quando le note scadono
- **Categorizzazione**: Visualizza note per categoria (Tutte, Oggi, Scadute, Completate)
- **Controllo Automatico**: Verifica automatica delle scadenze ogni ora
- **Interfaccia Intuitiva**: Design moderno e facile da usare

## 🚀 Installazione

1. **Clona il repository**:
   ```bash
   git clone <repository-url>
   cd flutter27072025
   ```

2. **Installa le dipendenze**:
   ```bash
   flutter pub get
   ```

3. **Esegui l'app**:
   ```bash
   flutter run
   ```

## ⚙️ Configurazione Email

Per ricevere le notifiche email, devi configurare le impostazioni SMTP:

### Per Gmail:

1. **Attiva l'autenticazione a 2 fattori** sul tuo account Google
2. Vai su **"Gestisci il tuo account Google"**
3. Seleziona **"Sicurezza"** → **"Password per le app"**
4. Genera una nuova password per l'app
5. Nell'app, vai su **Impostazioni** e inserisci:
   - **Email mittente**: la tua email Gmail
   - **Password app**: la password generata al punto 4
   - **Server SMTP**: `smtp.gmail.com`
   - **Porta SMTP**: `587`

### Per altri provider:

Consulta la documentazione del tuo provider email per i parametri SMTP corretti.

## 📱 Come Usare l'App

### Aggiungere una Nota

1. Tocca il pulsante **"+"** nella schermata principale
2. Inserisci **titolo** e **descrizione**
3. Seleziona la **data di scadenza**
4. (Opzionale) Inserisci un **indirizzo email** specifico per le notifiche
5. Tocca **"Salva Nota"**

### Gestire le Note

- **Visualizza per categoria**: Usa le tab in alto (Tutte, Oggi, Scadute, Completate)
- **Completa una nota**: Tocca i tre puntini → "Completa"
- **Modifica una nota**: Tocca i tre puntini → "Modifica"
- **Elimina una nota**: Tocca i tre puntini → "Elimina"

### Notifiche Automatiche

- L'app controlla automaticamente le scadenze ogni ora
- Riceverai un'email quando una nota scade
- Puoi forzare un controllo manuale toccando l'icona **"Aggiorna"**

## 🛠️ Tecnologie Utilizzate

- **Flutter**: Framework per lo sviluppo mobile
- **SQLite**: Database locale per la persistenza dei dati
- **Mailer**: Invio di email SMTP
- **WorkManager**: Esecuzione di task in background
- **SharedPreferences**: Salvataggio delle configurazioni

## 📋 Dipendenze Principali

```yaml
dependencies:
  flutter:
    sdk: flutter
  sqflite: ^2.4.2          # Database locale
  intl: ^0.19.0            # Internazionalizzazione
  mailer: ^6.1.2           # Invio email
  workmanager: ^0.8.0      # Task in background
  shared_preferences: ^2.3.2 # Configurazioni
```

## 🔧 Sviluppo

### Struttura del Progetto

```
lib/
├── models/
│   └── note_model.dart          # Modello dati per le note
├── services/
│   ├── database_helper.dart     # Gestione database SQLite
│   ├── email_service.dart       # Servizio invio email
│   └── notification_service.dart # Servizio notifiche background
├── screens/
│   ├── home_screen.dart         # Schermata principale
│   ├── add_note_screen.dart     # Schermata aggiunta/modifica note
│   └── settings_screen.dart     # Schermata impostazioni
└── main.dart                    # Entry point dell'app
```

### Test

Esegui i test con:
```bash
flutter test
```

## 🐛 Risoluzione Problemi

### Le email non vengono inviate

1. Verifica la configurazione SMTP nelle impostazioni
2. Assicurati di aver generato una "password per app" per Gmail
3. Controlla la connessione internet
4. Verifica che l'autenticazione a 2 fattori sia attiva

### Le notifiche in background non funzionano

1. Assicurati che l'app abbia i permessi necessari
2. Su Android, disabilita l'ottimizzazione della batteria per l'app
3. Verifica che le notifiche automatiche siano abilitate nelle impostazioni

## 📄 Licenza

Questo progetto è rilasciato sotto licenza MIT. Vedi il file `LICENSE` per i dettagli.

## 🤝 Contributi

I contributi sono benvenuti! Sentiti libero di aprire issue o pull request.

## 📞 Supporto

Per supporto o domande, apri un issue su GitHub.
